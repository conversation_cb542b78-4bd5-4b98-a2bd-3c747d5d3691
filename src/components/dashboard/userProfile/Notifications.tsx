"use client"
import <PERSON>ton from "@/components/ui/Button";
import Link from "next/link";


const Notifications = () => {
    return (
        <div className="w-full">
            <div className="flex justify-between items-center mb-6">
                <div>
                    <h2 className="text-xl font-semibold text-gray-900">
                        Notifications
                    </h2>
                    <p className="text-sm text-gray-500 mt-1">
                        Manage your notification preferences and communication settings
                    </p>
                </div>
                <Link
                    className="inline-flex items-center px-4 py-2 text-sm font-medium rounded-lg bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-sm hover:shadow-md transition-all duration-200 transform hover:scale-[1.02]"
                    href="/dashboard/assessments"
                >
                    Back to Dashboard
                </Link>
            </div>

                <div className="mt-10 space-y-10">
                <fieldset>
                    <legend className="text-sm font-semibold leading-6 text-gray-900">
                    By Email
                    </legend>
                    <div className="mt-6 space-y-6">
                    <div className="relative flex gap-x-3">
                        <div className="flex h-6 items-center">
                        <input
                            id="comments"
                            name="comments"
                            type="checkbox"
                            className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-600"
                        />
                        </div>
                        <div className="text-sm leading-6">
                        <label
                            htmlFor="comments"
                            className="font-medium text-gray-900"
                        >
                            Comments
                        </label>
                        <p className="text-gray-500">
                            Get notified when someones posts a comment on your
                            assignment.
                        </p>
                        </div>
                    </div>
                    <div className="relative flex gap-x-3">
                        <div className="flex h-6 items-center">
                        <input
                            id="evaluation"
                            name="evaluation"
                            type="checkbox"
                            className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-600"
                        />
                        </div>
                        <div className="text-sm leading-6">
                        <label
                            htmlFor="evaluation"
                            className="font-medium text-gray-900"
                        >
                            Evaluation
                        </label>
                        <p className="text-gray-500">
                            Get notified when someone evaluates your answers.
                        </p>
                        </div>
                    </div>
                    <div className="relative flex gap-x-3">
                        <div className="flex h-6 items-center">
                        <input
                            id="assignments"
                            name="assignments"
                            type="checkbox"
                            className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-600"
                        />
                        </div>
                        <div className="text-sm leading-6">
                        <label
                            htmlFor="assignments"
                            className="font-medium text-gray-900"
                        >
                            New Assignment
                        </label>
                        <p className="text-gray-500">
                            Get notified when you get assigned to any
                            test/assignment.
                        </p>
                        </div>
                    </div>
                    </div>
                </fieldset>

                <fieldset>
                    <legend className="text-sm font-semibold leading-6 text-gray-900">
                    Updates
                    </legend>
                    <div className="mt-6 space-y-6">
                    <div className="relative flex gap-x-3">
                        <div className="flex h-6 items-center">
                        <input
                            id="news"
                            name="updates"
                            type="checkbox"
                            className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-600"
                        />
                        </div>
                        <div className="text-sm leading-6">
                        <label
                            htmlFor="news"
                            className="font-medium text-gray-900"
                        >
                            News and Announcements
                        </label>
                        <p className="text-gray-500">
                            Get notified for recent news and announcements for
                            recent courses.
                        </p>
                        </div>
                    </div>
                    <div className="relative flex gap-x-3">
                        <div className="flex h-6 items-center">
                        <input
                            id="candidates"
                            name="candidates"
                            type="checkbox"
                            className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-600"
                        />
                        </div>
                        <div className="text-sm leading-6">
                        <label
                            htmlFor="candidates"
                            className="font-medium text-gray-900"
                        >
                            Weekly Modules/Courses updates
                        </label>
                        <p className="text-gray-500">
                            Get notified for your weekly module progress and updates
                            related to your course.
                        </p>
                        </div>
                    </div>
                    <div className="relative flex gap-x-3">
                        <div className="flex h-6 items-center">
                        <input
                            id="offers"
                            name="offers"
                            type="checkbox"
                            className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-600"
                        />
                        </div>
                        <div className="text-sm leading-6">
                        <label
                            htmlFor="offers"
                            className="font-medium text-gray-900"
                        >
                            Weekly assignment evaluation
                        </label>
                        <p className="text-gray-500">
                            Get notified for your weekly progress and
                            test/assignment results.
                        </p>
                        </div>
                    </div>
                    </div>
                </fieldset>

                <fieldset>
                    <legend className="text-sm font-semibold leading-6 text-gray-900">
                    Push Notifications
                    </legend>
                    <p className="mt-1 text-sm leading-6 text-gray-600">
                    These are delivered via SMS to your mobile phone.
                    </p>
                    <div className="mt-6 space-y-6">
                    <div className="flex items-center gap-x-3">
                        <input
                        id="push-everything"
                        name="push-notifications"
                        type="radio"
                        className="h-4 w-4 border-gray-300 text-indigo-600 focus:ring-indigo-600"
                        />
                        <label
                        htmlFor="push-everything"
                        className="block text-sm font-medium leading-6 text-gray-900"
                        >
                        Everything
                        </label>
                    </div>
                    <div className="flex items-center gap-x-3">
                        <input
                        id="push-email"
                        name="push-notifications"
                        type="radio"
                        className="h-4 w-4 border-gray-300 text-indigo-600 focus:ring-indigo-600"
                        />
                        <label
                        htmlFor="push-email"
                        className="block text-sm font-medium leading-6 text-gray-900"
                        >
                        Same as email
                        </label>
                    </div>
                    <div className="flex items-center gap-x-3">
                        <input
                        id="push-nothing"
                        name="push-notifications"
                        type="radio"
                        className="h-4 w-4 border-gray-300 text-indigo-600 focus:ring-indigo-600"
                        />
                        <label
                        htmlFor="push-nothing"
                        className="block text-sm font-medium leading-6 text-gray-900"
                        >
                        No push notifications
                        </label>
                    </div>
                    </div>
                </fieldset>
                </div>
                <div className="mt-6 flex justify-end gap-x-6 p-2">
                    <button
                        type="button"
                        className="text-sm font-semibold leading-6 text-gray-900"
                    >
                        Cancel
                    </button>
                    <button type="submit" className="flex gap-2 py-2 px-4 bg-textColor text-white rounded-md text-xs sm:text-sm lg:text-base">
                        Submit Profile
                    </button>
                    </div>
        
            </div>
        );
    };
    
    export default Notifications;