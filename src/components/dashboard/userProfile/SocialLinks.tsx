"use client";
import Button from "@/components/ui/Button";
import Link from "next/link";


const SocialLinks = () => {
    return (
        <div className="w-full">
            <div className="flex justify-between items-center mb-6">
                <div>
                    <h2 className="text-xl font-semibold text-gray-900">
                        Social Links
                    </h2>
                    <p className="text-sm text-gray-500 mt-1">
                        Connect your social profiles to showcase your professional presence
                    </p>
                </div>
                <Link
                    className="inline-flex items-center px-3 py-2 text-sm text-gray-600 hover:text-gray-800 transition-colors"
                    href="/dashboard/assessments"
                >
                    ← Back
                </Link>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label htmlFor="github" className="block text-sm font-medium text-gray-700 mb-1">
                        GitHub Profile
                    </label>
                    <input
                        type="url"
                        name="github"
                        id="github"
                        className="flex h-10 w-full border border-gray-300 bg-white rounded-md px-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all"
                        placeholder="https://github.com/username"
                    />
                </div>

                <div>
                    <label htmlFor="linkedin" className="block text-sm font-medium text-gray-700 mb-1">
                        LinkedIn Profile
                    </label>
                    <input
                        type="url"
                        name="linkedin"
                        id="linkedin"
                        className="flex h-10 w-full border border-gray-300 bg-white rounded-md px-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all"
                        placeholder="https://linkedin.com/in/username"
                    />
                </div>

                <div>
                    <label htmlFor="twitter" className="block text-sm font-medium text-gray-700 mb-1">
                        Twitter Profile
                    </label>
                    <input
                        type="url"
                        name="twitter"
                        id="twitter"
                        className="flex h-10 w-full border border-gray-300 bg-white rounded-md px-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all"
                        placeholder="https://twitter.com/username"
                    />
                </div>

                <div>
                    <label htmlFor="portfolio" className="block text-sm font-medium text-gray-700 mb-1">
                        Portfolio Website
                    </label>
                    <input
                        type="url"
                        name="portfolio"
                        id="portfolio"
                        className="flex h-10 w-full border border-gray-300 bg-white rounded-md px-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all"
                        placeholder="https://yourportfolio.com"
                    />
                </div>
            </div>

            <div className="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
                <button
                    type="button"
                    className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all"
                >
                    Cancel
                </button>
                <button
                    type="submit"
                    className="px-6 py-2 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white rounded-md font-medium transition-all focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                >
                    Save Changes
                </button>
            </div>
        </div>

        );
    };
    
    export default SocialLinks;