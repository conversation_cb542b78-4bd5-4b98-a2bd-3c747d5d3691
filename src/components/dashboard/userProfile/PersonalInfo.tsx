"use client";
import { ChangeEvent, useEffect, useState } from "react";
import Button from "@/components/ui/Button";
import Link from "next/link";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";

interface Country {
    name: string;
    code: string;
  }

  
const PersonalInfo = () => {
    const [selectedDate, setselectedDate] = useState(null);
    const [countries, setCountries] = useState<Country[]>([]);
    const [selectedCountry, setSelectedCountry] = useState<string>("");

    useEffect(() => {
        fetch("/countries.json")
            .then((response) => response.json())
            .then((data) => setCountries(data))
            .catch((error) => console.error("Error fetching countries:", error));
        }, []);
        
        const handleCountryChange = (event: ChangeEvent<HTMLSelectElement>) => {
            setSelectedCountry(event.target.value);
        };

    return (
        <div className="w-full">
            <div className="flex justify-between items-center mb-6">
                <div>
                    <h2 className="text-xl font-semibold text-gray-900">
                        Personal Information
                    </h2>
                    <p className="text-sm text-gray-500 mt-1">
                        Update your personal details and contact information
                    </p>
                </div>
                <Link
                    className="inline-flex items-center px-3 py-2 text-sm text-gray-600 hover:text-gray-800 transition-colors"
                    href="/dashboard/assessments"
                >
                    ← Back
                </Link>
            </div>

            <div className="mb-6">
                <label
                    htmlFor="bio"
                    className="block text-sm font-medium text-gray-700 mb-1"
                >
                    Bio
                </label>
                <textarea
                    id="bio"
                    name="bio"
                    rows={4}
                    className="flex min-h-[100px] w-full border border-gray-300 bg-white rounded-md px-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all"
                    placeholder="Write a few sentences about your professional background and interests..."
                    defaultValue=""
                />
                <p className="mt-1 text-xs text-gray-500">
                    Brief description of your professional background and interests
                </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label
                        htmlFor="dateofbirth"
                        className="block text-sm font-medium text-gray-700 mb-1"
                    >
                        Date of Birth
                    </label>
                    <DatePicker
                        id="dateofbirth"
                        className="flex h-10 w-full border border-gray-300 bg-white rounded-md px-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all"
                        selected={selectedDate}
                        onChange={(date) => setselectedDate(date)}
                        dateFormat="dd/MM/yyyy"
                        placeholderText="Select your date of birth"
                        showYearDropdown
                        showIcon
                        required
                    />
                </div>

                <div>
                    <label
                        htmlFor="phone"
                        className="block text-sm font-medium text-gray-700 mb-1"
                    >
                        Phone Number
                    </label>
                    <input
                        type="tel"
                        id="phone"
                        className="flex h-10 w-full border border-gray-300 bg-white rounded-md px-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all"
                        placeholder="Enter your phone number"
                        required
                    />
                </div>

                <div>
                    <label
                        htmlFor="gender"
                        className="block text-sm font-medium text-gray-700 mb-1"
                    >
                        Gender
                    </label>
                    <select
                        id="gender"
                        name="gender"
                        autoComplete="sex"
                        required
                        className="h-10 w-full border border-gray-300 bg-white rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all"
                    >
                        <option value="" disabled>
                            Select your gender
                        </option>
                        <option value="male">Male</option>
                        <option value="female">Female</option>
                        <option value="other">Other</option>
                        <option value="prefer-not-to-say">Prefer not to say</option>
                    </select>
                </div>

                <div>
                    <label
                        htmlFor="country"
                        className="block text-sm font-medium text-gray-700 mb-1"
                    >
                        Country
                    </label>
                    <select
                        id="country"
                        className="h-10 w-full border border-gray-300 bg-white rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all"
                        required
                        value={selectedCountry}
                        onChange={handleCountryChange}
                    >
                        <option value="" disabled>
                            Select your country
                        </option>
                        {countries.map((country) => (
                            <option key={country.code} value={country.code}>
                                {country.name}
                            </option>
                        ))}
                    </select>
                </div>

                <div className="md:col-span-2">
                    <label
                        htmlFor="street-address"
                        className="block text-sm font-medium text-gray-700 mb-1"
                    >
                        Street Address
                    </label>
                    <input
                        type="text"
                        name="street-address"
                        id="street-address"
                        autoComplete="street-address"
                        required
                        className="flex h-10 w-full border border-gray-300 bg-white rounded-md px-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all"
                        placeholder="Enter your street address"
                    />
                </div>

                <div>
                    <label
                        htmlFor="city"
                        className="block text-sm font-medium text-gray-700 mb-1"
                    >
                        City
                    </label>
                    <input
                        type="text"
                        name="city"
                        id="city"
                        autoComplete="address-level2"
                        required
                        className="flex h-10 w-full border border-gray-300 bg-white rounded-md px-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all"
                        placeholder="Enter your city"
                    />
                </div>

                <div>
                    <label
                        htmlFor="region"
                        className="block text-sm font-medium text-gray-700 mb-1"
                    >
                        State / Province
                    </label>
                    <input
                        type="text"
                        name="region"
                        id="region"
                        required
                        autoComplete="address-level1"
                        className="flex h-10 w-full border border-gray-300 bg-white rounded-md px-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all"
                        placeholder="Enter your state/province"
                    />
                </div>

                <div>
                    <label
                        htmlFor="postal-code"
                        className="block text-sm font-medium text-gray-700 mb-1"
                    >
                        ZIP / Postal Code
                    </label>
                    <input
                        type="text"
                        name="postal-code"
                        id="postal-code"
                        autoComplete="postal-code"
                        required
                        className="flex h-10 w-full border border-gray-300 bg-white rounded-md px-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all"
                        placeholder="Enter your postal code"
                    />
                </div>
            </div>

            <div className="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
                <button
                    type="button"
                    className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all"
                >
                    Cancel
                </button>
                <button
                    type="submit"
                    className="px-6 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-md font-medium transition-all focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                >
                    Save Changes
                </button>
            </div>
        </div>

        );
    };
    
    export default PersonalInfo;