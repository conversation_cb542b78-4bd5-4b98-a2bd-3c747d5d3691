"use client";
import { ChangeEvent, useEffect, useState } from "react";
import Button from "@/components/ui/Button";
import Link from "next/link";


const EduQualification = () => {
    const [courses, setCourses] = useState({});
    const [selectedCourse, setSelectedCourse] = useState('');
    

    useEffect(() => {
        // Fetch the JSON data
        fetch('/courses.json')
            .then((response) => response.json())
            .then((data) => setCourses(data))
            .catch((error) => console.error('Error fetching courses:', error));
        }, []);
        
        const handleCourseChange = (event: ChangeEvent<HTMLSelectElement>) => {
            setSelectedCourse(event.target.value);
            console.log('Selected value:', event.target.value);
            console.log('Selected Course:', event.target.value);
        };
        
    return (
        <div className="w-full">
            <div className="flex justify-between items-center mb-6">
                <div>
                    <h2 className="text-xl font-semibold text-gray-900">
                        Educational Qualification
                    </h2>
                    <p className="text-sm text-gray-500 mt-1">
                        Add your highest educational qualification and academic details
                    </p>
                </div>
                <Link
                    className="inline-flex items-center px-3 py-2 text-sm text-gray-600 hover:text-gray-800 transition-colors"
                    href="/dashboard/assessments"
                >
                    ← Back
                </Link>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div>
                    <label htmlFor="courseDropdown" className="block text-sm font-medium text-gray-700 mb-1">
                        Degree / Course
                    </label>
                    <select
                        id="courseDropdown"
                        className="h-10 w-full border border-gray-300 bg-white rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all"
                        value={selectedCourse}
                        onChange={handleCourseChange}
                        data-testid="courseDropdown"
                    >
                        <option value="" disabled>Select your course</option>
                        {Object.keys(courses).map((category) => (
                            <optgroup key={category} label={category}>
                                {Object.entries(courses[category]).map(([courseName, courseShortForm]) => (
                                    <option key={courseShortForm} value={courseShortForm}>
                                        {courseName} ({courseShortForm})
                                    </option>
                                ))}
                            </optgroup>
                        ))}
                    </select>
                </div>

                <div>
                    <label htmlFor="university-name" className="block text-sm font-medium text-gray-700 mb-1">
                        University Name
                    </label>
                    <input
                        type="text"
                        id="university-name"
                        name="university-name"
                        autoComplete="organization"
                        required
                        className="flex h-10 w-full border border-gray-300 bg-white rounded-md px-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all"
                        placeholder="Enter university name"
                    />
                </div>

                <div>
                    <label htmlFor="college-name" className="block text-sm font-medium text-gray-700 mb-1">
                        College Name
                    </label>
                    <input
                        type="text"
                        id="college-name"
                        name="college-name"
                        autoComplete="organization"
                        className="flex h-10 w-full border border-gray-300 bg-white rounded-md px-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all"
                        placeholder="Enter college name"
                    />
                </div>

                <div>
                    <label htmlFor="specialisation-name" className="block text-sm font-medium text-gray-700 mb-1">
                        Specialization
                    </label>
                    <input
                        type="text"
                        id="specialisation-name"
                        name="specialisation-name"
                        className="flex h-10 w-full border border-gray-300 bg-white rounded-md px-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all"
                        placeholder="e.g., Computer Science"
                    />
                </div>

                <div>
                    <label htmlFor="year" className="block text-sm font-medium text-gray-700 mb-1">
                        Year of Passing
                    </label>
                    <input
                        type="number"
                        id="year"
                        name="year"
                        min="1950"
                        max="2030"
                        required
                        className="flex h-10 w-full border border-gray-300 bg-white rounded-md px-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all"
                        placeholder="e.g., 2023"
                    />
                </div>

                <div>
                    <label htmlFor="cgpa" className="block text-sm font-medium text-gray-700 mb-1">
                        CGPA / Percentage
                    </label>
                    <input
                        type="text"
                        id="cgpa"
                        name="cgpa"
                        required
                        className="flex h-10 w-full border border-gray-300 bg-white rounded-md px-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all"
                        placeholder="e.g., 8.5 or 85%"
                    />
                </div>
            </div>

            <div className="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
                <button
                    type="button"
                    className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all"
                >
                    Cancel
                </button>
                <button
                    type="submit"
                    className="px-6 py-2 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white rounded-md font-medium transition-all focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                >
                    Save Changes
                </button>
            </div>
        </div>

        );
    };
    
    export default EduQualification;