"use client"
import Button from "@/components/ui/Button";
import Link from "next/link";

const ItSkills = () => {
    return (
        <div className="w-full">
            <div className="flex justify-between items-center mb-6">
                <div>
                    <h2 className="text-xl font-semibold text-gray-900">IT Skills</h2>
                    <p className="text-sm text-gray-500 mt-1">Add and manage your professional IT skills and expertise</p>
                </div>
                <Link
                    className="inline-flex items-center px-3 py-2 text-sm text-gray-600 hover:text-gray-800 transition-colors"
                    href="/dashboard/assessments"
                >
                    ← Back
                </Link>
            </div>

            <div className="mb-6">
                <label htmlFor="about" className="block text-sm font-medium text-gray-700 mb-1">
                    IT Skills
                </label>
                <textarea
                    id="about"
                    required
                    name="about"
                    rows={4}
                    className="flex min-h-[100px] w-full border border-gray-300 bg-white rounded-md px-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all"
                    placeholder="List your technical skills, programming languages, frameworks, tools, etc..."
                    defaultValue=""
                />
                <p className="mt-1 text-xs text-gray-500">
                    Include programming languages, frameworks, tools, and technologies you're proficient in
                </p>
            </div>

            <div className="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
                <button
                    type="button"
                    className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all"
                >
                    Cancel
                </button>
                <button
                    type="submit"
                    className="px-6 py-2 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white rounded-md font-medium transition-all focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                >
                    Save Changes
                </button>
            </div>
        </div>

        );
    };
    
    export default ItSkills;