"use client"
import Button from "@/components/ui/Button";
import Link from "next/link";

const ItSkills = () => {
    return (
        <div className="w-full">
            <div className="flex justify-between items-center mb-6">
                <div>
                    <h2 className="text-xl font-semibold text-gray-900">IT Skills</h2>
                    <p className="text-sm text-gray-500 mt-1">Add and manage your professional IT skills and expertise</p>
                </div>
                <Link
                    className="inline-flex items-center px-3 py-2 text-sm text-gray-600 hover:text-gray-800 transition-colors"
                    href="/dashboard/assessments"
                >
                    ← Back
                </Link>
            </div>

            <div className="col-span-full mt-5">
                <label htmlFor="about" className="block text-sm font-medium leading-6 text-gray-900">
                IT Skills:
                </label>
                <div className="mt-2">
                <textarea
                    id="about"
                    required
                    name="about"
                    rows={3}
                    className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                    defaultValue={""}
                />
                </div>
            </div>

            <div className="mt-6 flex justify-end gap-x-6 p-2">
                <button type="button" className="text-sm font-semibold leading-6 text-gray-900">
                Cancel
                </button>
                <button type="submit" className="flex gap-2 py-2 px-4 bg-textColor text-white rounded-md text-xs sm:text-sm lg:text-base">
                Save Changes
                </button>
            </div>
            </div>

        );
    };
    
    export default ItSkills;