"use client";
import <PERSON>ton from "@/components/ui/Button";
import Link from "next/link";
import { PhotoIcon, UserCircleIcon } from "@heroicons/react/24/outline";
import { useState } from "react";
import { getUser } from "@/api/user.localStorage";


const GeneralInfo = () => {
    const user = getUser();
    const [selectedFile, setSelectedFile] = useState(null);

    const handlephotofile = (event) => {
        setSelectedFile(event.target.files[0]);

        const formData = new FormData();
        formData.append('file', event.target.files[0]);
        const blob = new Blob([event.target.files[0]]);

        console.log("photo uploaded", blob);

    };

    const [email, setEmail] = useState("");
    const [emailError, setEmailError] = useState("");

    const validateEmail = (email) => {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    };

    const handleEmailChange = (e) => {
        const newEmail = e.target.value;
        setEmail(newEmail);

        if (!validateEmail(newEmail)) {
        setEmailError("Please enter a valid email address.");
        } else {
        setEmailError("");
        }
        console.log("Email is validated");
    };


    return (
        <div className="w-full">
            <div className="flex justify-between items-center mb-6">
                <div>
                    <h2 className="text-xl font-semibold text-gray-900">
                        General Information
                    </h2>
                    <p className="text-sm text-gray-500 mt-1">
                        Update your basic profile information and upload your resume
                    </p>
                </div>

                <Link
                    className="inline-flex items-center px-3 py-2 text-sm text-gray-600 hover:text-gray-800 transition-colors"
                    href="/dashboard/assessments"
                >
                    ← Back
                </Link>
            </div>

            <div className="mb-8">
                <label className="block text-sm font-medium text-gray-700 mb-3">
                    Profile Photo
                </label>
                <div className="flex items-center gap-4">
                    <div className="relative">
                        <UserCircleIcon
                            className="h-20 w-20 text-gray-400 bg-gray-100 rounded-full p-2"
                            aria-hidden="true"
                        />
                    </div>
                    <div>
                        <label
                            htmlFor="file-upload"
                            className="inline-flex items-center px-4 py-2 text-sm font-medium rounded-lg border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200 cursor-pointer"
                        >
                            <span>Upload Photo</span>
                            <input
                                id="file-upload"
                                name="file-upload"
                                type="file"
                                accept="image/*"
                                className="sr-only"
                                onChange={(e) => handlephotofile(e)}
                            />
                        </label>
                        <p className="text-xs text-gray-500 mt-1">
                            JPG, PNG or GIF up to 5MB
                        </p>
                    </div>
                </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label
                        htmlFor="username"
                        className="block text-sm font-medium text-gray-700 mb-1"
                    >
                        Username
                    </label>
                    <input
                        type="text"
                        name="username"
                        id="username"
                        autoComplete="username"
                        className="flex h-10 w-full border border-gray-300 bg-white rounded-md px-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all"
                        placeholder="Enter username"
                        defaultValue={user?.user_full_name}
                    />
                </div>

                <div>
                    <label
                        htmlFor="email"
                        className="block text-sm font-medium text-gray-700 mb-1"
                    >
                        Email Address
                    </label>
                    <input
                        type="email"
                        id="email"
                        value={user?.email}
                        onChange={handleEmailChange}
                        disabled={true}
                        className="flex h-10 w-full border border-gray-300 bg-gray-50 rounded-md px-3 py-2 text-sm text-gray-500 cursor-not-allowed"
                    />
                    {emailError && (<p className="text-red-500 text-sm mt-1">{emailError}</p>)}
                </div>

                <div>
                    <label
                        htmlFor="first-name"
                        className="block text-sm font-medium text-gray-700 mb-1"
                    >
                        First Name
                    </label>
                    <input
                        type="text"
                        name="first-name"
                        id="first-name"
                        autoComplete="given-name"
                        required
                        className="flex h-10 w-full border border-gray-300 bg-white rounded-md px-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all"
                        placeholder="Enter first name"
                    />
                </div>

                <div>
                    <label
                        htmlFor="last-name"
                        className="block text-sm font-medium text-gray-700 mb-1"
                    >
                        Last Name
                    </label>
                    <input
                        type="text"
                        name="last-name"
                        id="last-name"
                        autoComplete="family-name"
                        required
                        className="flex h-10 w-full border border-gray-300 bg-white rounded-md px-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all"
                        placeholder="Enter last name"
                    />
                </div>

                <div>
                    <label
                        htmlFor="jobprofile"
                        className="block text-sm font-medium text-gray-700 mb-1"
                    >
                        Job Profile
                    </label>
                    <input
                        type="text"
                        id="jobprofile"
                        className="flex h-10 w-full border border-gray-300 bg-white rounded-md px-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all"
                        placeholder="e.g., Software Engineer"
                        required
                    />
                </div>

                <div>
                    <label
                        htmlFor="company-name"
                        className="block text-sm font-medium text-gray-700 mb-1"
                    >
                        Company Name
                    </label>
                    <input
                        type="text"
                        id="company-name"
                        className="flex h-10 w-full border border-gray-300 bg-white rounded-md px-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all"
                        placeholder="Enter company name"
                        required
                    />
                </div>

                <div className="md:col-span-2">
                    <label
                        htmlFor="experience"
                        className="block text-sm font-medium text-gray-700 mb-1"
                    >
                        Work Experience (years)
                    </label>
                    <input
                        type="number"
                        id="experience"
                        min="0"
                        max="50"
                        className="flex h-10 w-full border border-gray-300 bg-white rounded-md px-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all"
                        placeholder="0"
                        required
                    />
                </div>

                <div className="md:col-span-2">
                    <label
                        htmlFor="resume-upload"
                        className="block text-sm font-medium text-gray-700 mb-1"
                    >
                        Resume Upload
                    </label>
                    <div className="mt-2 flex justify-center rounded-lg border-2 border-dashed border-gray-300 px-6 py-10 hover:border-gray-400 transition-colors">
                        <div className="text-center">
                            <PhotoIcon
                                className="mx-auto h-12 w-12 text-gray-400"
                                aria-hidden="true"
                            />
                            <div className="mt-4 flex text-sm leading-6 text-gray-600">
                                <label
                                    htmlFor="resume-upload"
                                    className="relative cursor-pointer rounded-md bg-white font-semibold text-blue-600 focus-within:outline-none focus-within:ring-2 focus-within:ring-blue-500 focus-within:ring-offset-2 hover:text-blue-500"
                                >
                                    <span>Upload a file</span>
                                    <input
                                        id="resume-upload"
                                        name="resume-upload"
                                        type="file"
                                        accept=".pdf,.doc,.docx"
                                        className="sr-only"
                                    />
                                </label>
                                <p className="pl-1">or drag and drop</p>
                            </div>
                            <p className="text-xs leading-5 text-gray-500">
                                PDF, DOC, DOCX up to 10MB
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <div className="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
                <button
                    type="button"
                    className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all"
                >
                    Cancel
                </button>
                <button
                    type="submit"
                    className="px-6 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-md font-medium transition-all focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                >
                    Save Changes
                </button>
            </div>
        </div>

            
        );
    };
    
    export default GeneralInfo;