"use client";
import NavigationHeader from "@/components/ui/header/NavigationHeader";
import { useState } from "react";
import { InformationCircleIcon, UserIcon, AcademicCapIcon, LinkIcon, ComputerDesktopIcon, BellIcon } from "@heroicons/react/24/outline";
import { toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import GeneralInfo from "@/components/dashboard/userProfile/GeneralInfo";
import EduQualification from "@/components/dashboard/userProfile/EduQualification";
import SocialLinks from "@/components/dashboard/userProfile/SocialLinks";
import ItSkills from "@/components/dashboard/userProfile/ItSkills";
import PersonalInfo from "@/components/dashboard/userProfile/PersonalInfo";
import Notifications from "@/components/dashboard/userProfile/Notifications";

const UserProfile = () => {
  const [selectedComponent, setSelectedComponent] = useState('GeneralInfo');

  const renderComponent = () => {
    switch (selectedComponent) {
      case 'GeneralInfo':
        return <GeneralInfo />;
      case 'EduQualification':
        return <EduQualification />;
      case 'SocialLinks':
        return <SocialLinks />;
      case 'ItSkills':
        return <ItSkills />;
      case 'PersonalInfo':
        return <PersonalInfo />;
      case 'Notifications':
        return <Notifications />;
      default:
        return <GeneralInfo />;
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    // Handle form submission
    console.log('Form submitted:');
  };

  const navigationItems = [
    { id: 'GeneralInfo', label: 'General Info', icon: UserIcon },
    { id: 'EduQualification', label: 'Education', icon: AcademicCapIcon },
    { id: 'PersonalInfo', label: 'Personal Info', icon: UserIcon },
    { id: 'SocialLinks', label: 'Social Links', icon: LinkIcon },
    { id: 'ItSkills', label: 'IT Skills', icon: ComputerDesktopIcon },
    { id: 'Notifications', label: 'Notifications', icon: BellIcon },
  ];

  return (
    <>
      <NavigationHeader />
      <main className="w-full flex flex-col p-4 relative overflow-auto h-full bg-gray-50">
        <div className="w-full max-w-7xl mx-auto">
          {/* Header Section */}
          <div className="bg-white rounded-xl shadow-sm p-6 mb-6">
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-4">
                <h1 className="text-2xl font-bold text-gray-900">User Profile</h1>
                <button
                  onClick={() => toast.info('Manage your profile information, education, skills, and preferences.')}
                  className="text-gray-500 hover:text-blue-600 transition-colors"
                  aria-label="Information"
                >
                  <InformationCircleIcon className="w-5 h-5" />
                </button>
              </div>
              <div className="text-sm text-gray-500">
                Profile Management
              </div>
            </div>
          </div>

          <div className="flex flex-col lg:flex-row gap-6">
            {/* Navigation Sidebar */}
            <aside className="lg:w-64">
              <div className="bg-white rounded-xl shadow-sm p-4">
                <nav className="space-y-2">
                  {navigationItems.map((item) => {
                    const Icon = item.icon;
                    return (
                      <button
                        key={item.id}
                        onClick={() => setSelectedComponent(item.id)}
                        className={`w-full flex items-center gap-3 px-4 py-3 text-left rounded-lg transition-all duration-200 ${
                          selectedComponent === item.id
                            ? 'bg-blue-50 text-blue-700 border-l-4 border-blue-600'
                            : 'text-gray-700 hover:bg-gray-50 hover:text-blue-600'
                        }`}
                      >
                        <Icon className="h-5 w-5" />
                        <span className="font-medium">{item.label}</span>
                      </button>
                    );
                  })}
                </nav>
              </div>
            </aside>

            {/* Content Area */}
            <div className="flex-1">
              <div className="bg-white rounded-xl shadow-sm p-6">
                <form onSubmit={handleSubmit} className="space-y-6">
                  {renderComponent()}
                </form>
              </div>
            </div>
          </div>
        </div>
      </main>
    </>
  );
};

export default UserProfile;
